// WebGL增强版ESP实现 - 替换Canvas 2D渲染 + 检测规避 + 自瞄功能 - v3.0
// 新增功能：
// - 自瞄系统 (Ctrl+A 切换)
// - 基于esp-first.js的aimbot实现
// - 支持头部瞄准和距离限制
(function() {
    'use strict';
    
    // 全局配置
    const CONFIG = {
        updateInterval: 8, // 越小绘制越流畅，推荐设置为12(60fps)
        maxDrawDistance: 500,
        enableBoxESP: true,
        enableLineESP: false, // 移除连线功能
        enableNameTags: true,
        autoHideUI: true, // 启用UI自动隐藏功能
        autoHideDelay: 10000, // UI自动隐藏延迟时间（毫秒）
        enableAimbot: false, // 自瞄功能开关
        aimHeadshot: true, // 是否瞄准头部
        aimSmoothness: 0.3, // 自瞄平滑度 (0-1)
        aimMaxDistance: 200, // 自瞄最大距离
        debug: false
    };
    
    let gameEngineReady = false;
    let ESPInstance = null;
    let engineCheckInterval = null;
    
    // WebGL渲染系统 - 替换Canvas 2D
    class WebGLRenderer {
        constructor() {
            this.canvas = null;
            this.gl = null;
            this.program = null;
            this.isInitialized = false;
            this.vertexBuffer = null;
            this.colorBuffer = null;
            this.vertices = [];
            this.colors = [];

            // 检测规避相关
            this.originalGetContext = HTMLCanvasElement.prototype.getContext;
            this.originalGetParameter = null;
            this.setupAntiDetection();
        }

        // 增强的检测规避设置
        setupAntiDetection() {
            const self = this;

            // 劫持 getContext 方法
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                const context = self.originalGetContext.call(this, contextType, contextAttributes);

                if (contextType === 'webgl' || contextType === 'webgl2') {
                    // 保存原始方法
                    if (!self.originalGetParameter) {
                        self.originalGetParameter = context.getParameter;
                        self.originalGetExtension = context.getExtension;
                        self.originalGetShaderPrecisionFormat = context.getShaderPrecisionFormat;
                    }

                    // 劫持 getParameter 方法来隐藏WebGL指纹
                    context.getParameter = function(parameter) {
                        // 返回通用值来规避检测
                        switch (parameter) {
                            case context.VENDOR:
                                return 'WebKit';
                            case context.RENDERER:
                                return 'WebKit WebGL';
                            case context.VERSION:
                                return 'WebGL 1.0';
                            case context.SHADING_LANGUAGE_VERSION:
                                return 'WebGL GLSL ES 1.0';
                            case context.UNMASKED_VENDOR_WEBGL:
                                return 'WebKit';
                            case context.UNMASKED_RENDERER_WEBGL:
                                return 'WebKit WebGL';
                            case context.MAX_TEXTURE_SIZE:
                                return 4096; // 通用值
                            case context.MAX_VIEWPORT_DIMS:
                                return new Int32Array([4096, 4096]);
                            case context.MAX_VERTEX_ATTRIBS:
                                return 16;
                            case context.MAX_VERTEX_UNIFORM_VECTORS:
                                return 256;
                            case context.MAX_FRAGMENT_UNIFORM_VECTORS:
                                return 256;
                            case context.MAX_VARYING_VECTORS:
                                return 8;
                            default:
                                return self.originalGetParameter.call(this, parameter);
                        }
                    };

                    // 劫持扩展获取
                    context.getExtension = function(name) {
                        // 限制返回的扩展以减少指纹
                        const allowedExtensions = [
                            'OES_texture_float',
                            'OES_texture_half_float',
                            'WEBGL_lose_context'
                        ];

                        if (allowedExtensions.includes(name)) {
                            return self.originalGetExtension.call(this, name);
                        }
                        return null;
                    };

                    // 劫持精度格式查询
                    context.getShaderPrecisionFormat = function(_, __) {
                        // 返回标准化的精度信息，忽略参数以规避检测
                        return {
                            rangeMin: 127,
                            rangeMax: 127,
                            precision: 23
                        };
                    };
                }

                return context;
            };

            // 添加随机化延迟以规避时序检测
            this.randomDelay = () => {
                return Math.random() * 2 + 1; // 1-3ms随机延迟
            };

            // 劫持Canvas 2D指纹检测
            this.setupCanvasAntiDetection();
        }

        // Canvas 2D 反检测
        setupCanvasAntiDetection() {
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

            // 劫持 toDataURL 方法
            HTMLCanvasElement.prototype.toDataURL = function(type, quality) {
                // 添加微小的随机噪声来改变指纹
                const ctx = this.getContext('2d');
                if (ctx && this.width > 0 && this.height > 0) {
                    const imageData = ctx.getImageData(0, 0, this.width, this.height);
                    const data = imageData.data;

                    // 在随机位置添加微小噪声
                    for (let i = 0; i < 10; i++) {
                        const randomIndex = Math.floor(Math.random() * data.length / 4) * 4;
                        if (randomIndex < data.length - 3) {
                            data[randomIndex] = (data[randomIndex] + Math.floor(Math.random() * 3) - 1) % 256;
                            data[randomIndex + 1] = (data[randomIndex + 1] + Math.floor(Math.random() * 3) - 1) % 256;
                            data[randomIndex + 2] = (data[randomIndex + 2] + Math.floor(Math.random() * 3) - 1) % 256;
                        }
                    }

                    ctx.putImageData(imageData, 0, 0);
                }

                return originalToDataURL.call(this, type, quality);
            };

            // 劫持 getImageData 方法
            CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {
                const imageData = originalGetImageData.call(this, sx, sy, sw, sh);

                // 添加微小的随机变化
                if (Math.random() < 0.1) { // 10%的概率添加噪声
                    const data = imageData.data;
                    const randomIndex = Math.floor(Math.random() * data.length / 4) * 4;
                    if (randomIndex < data.length - 3) {
                        data[randomIndex] = (data[randomIndex] + 1) % 256;
                    }
                }

                return imageData;
            };
        }

        init() {
            if (this.isInitialized) return;

            this.canvas = document.createElement('canvas');

            // 尝试获取WebGL上下文，带有反检测属性
            const contextOptions = {
                alpha: true,
                antialias: false,
                depth: false,
                stencil: false,
                preserveDrawingBuffer: false,
                powerPreference: 'default',
                failIfMajorPerformanceCaveat: false
            };

            this.gl = this.canvas.getContext('webgl', contextOptions) ||
                     this.canvas.getContext('experimental-webgl', contextOptions);

            if (!this.gl) {
                console.error("[FGE] WebGL not supported, falling back to 2D");
                this.fallbackTo2D();
                return;
            }

            this.canvas.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                pointer-events: none;
                z-index: 1000;
                opacity: 0.8;
            `;

            this.setupWebGL();
            this.resizeCanvas();
            window.addEventListener('resize', () => this.resizeCanvas());

            setTimeout(() => {
                document.body.appendChild(this.canvas);
                console.log("[FGE] WebGL Renderer Initialized");
            }, 500);

            this.isInitialized = true;
        }

        // 2D Canvas 后备方案
        fallbackTo2D() {
            this.ctx = this.canvas.getContext('2d');
            this.isWebGL = false;

            this.canvas.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                pointer-events: none;
                z-index: 1000;
                opacity: 0.8;
            `;

            this.resizeCanvas();
            window.addEventListener('resize', () => this.resizeCanvas());

            setTimeout(() => {
                document.body.appendChild(this.canvas);
                console.log("[FGE] 2D Canvas Fallback Initialized");
            }, 500);

            this.isInitialized = true;
        }

        resizeCanvas() {
            if (!this.canvas) return;
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;

            // 同时调整文字canvas大小
            if (this.textCanvas) {
                this.textCanvas.width = this.canvas.width;
                this.textCanvas.height = this.canvas.height;
            }

            if (this.gl) {
                this.gl.viewport(0, 0, this.canvas.width, this.canvas.height);
            }
        }

        clear() {
            if (this.gl) {
                this.gl.clearColor(0.0, 0.0, 0.0, 0.0);
                this.gl.clear(this.gl.COLOR_BUFFER_BIT);
                this.vertices = [];
                this.colors = [];
            } else if (this.ctx) {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }

            // 清除文字层
            this.clearText();
        }

        // 设置WebGL着色器和程序
        setupWebGL() {
            const vertexShaderSource = `
                attribute vec2 a_position;
                attribute vec4 a_color;
                varying vec4 v_color;
                uniform vec2 u_resolution;

                void main() {
                    vec2 zeroToOne = a_position / u_resolution;
                    vec2 zeroToTwo = zeroToOne * 2.0;
                    vec2 clipSpace = zeroToTwo - 1.0;
                    gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);
                    v_color = a_color;
                }
            `;

            const fragmentShaderSource = `
                precision mediump float;
                varying vec4 v_color;

                void main() {
                    gl_FragColor = v_color;
                }
            `;

            const vertexShader = this.createShader(this.gl.VERTEX_SHADER, vertexShaderSource);
            const fragmentShader = this.createShader(this.gl.FRAGMENT_SHADER, fragmentShaderSource);

            this.program = this.createProgram(vertexShader, fragmentShader);

            // 获取属性和uniform位置
            this.positionAttributeLocation = this.gl.getAttribLocation(this.program, "a_position");
            this.colorAttributeLocation = this.gl.getAttribLocation(this.program, "a_color");
            this.resolutionUniformLocation = this.gl.getUniformLocation(this.program, "u_resolution");

            // 创建缓冲区
            this.vertexBuffer = this.gl.createBuffer();
            this.colorBuffer = this.gl.createBuffer();

            // 启用混合以支持透明度
            this.gl.enable(this.gl.BLEND);
            this.gl.blendFunc(this.gl.SRC_ALPHA, this.gl.ONE_MINUS_SRC_ALPHA);
        }

        // 创建着色器
        createShader(type, source) {
            const shader = this.gl.createShader(type);
            this.gl.shaderSource(shader, source);
            this.gl.compileShader(shader);

            if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {
                console.error("[FGE] Shader compilation error:", this.gl.getShaderInfoLog(shader));
                this.gl.deleteShader(shader);
                return null;
            }

            return shader;
        }

        // 创建程序
        createProgram(vertexShader, fragmentShader) {
            const program = this.gl.createProgram();
            this.gl.attachShader(program, vertexShader);
            this.gl.attachShader(program, fragmentShader);
            this.gl.linkProgram(program);

            if (!this.gl.getProgramParameter(program, this.gl.LINK_STATUS)) {
                console.error("[FGE] Program linking error:", this.gl.getProgramInfoLog(program));
                this.gl.deleteProgram(program);
                return null;
            }

            return program;
        }

        // 添加矩形到渲染队列
        addRectangle(x, y, width, height, color) {
            if (!this.gl) return;

            // 将颜色字符串转换为RGBA值
            const rgba = this.hexToRgba(color);

            // 矩形的6个顶点（两个三角形）
            const x1 = x, y1 = y;
            const x2 = x + width, y2 = y + height;

            // 第一个三角形
            this.vertices.push(x1, y1, x2, y1, x1, y2);
            // 第二个三角形
            this.vertices.push(x1, y2, x2, y1, x2, y2);

            // 为每个顶点添加颜色
            for (let i = 0; i < 6; i++) {
                this.colors.push(rgba.r, rgba.g, rgba.b, rgba.a);
            }
        }

        // 添加线条到渲染队列
        addLine(x1, y1, x2, y2, color, thickness = 2) {
            if (!this.gl) return;

            // 计算线条的方向向量
            const dx = x2 - x1;
            const dy = y2 - y1;
            const length = Math.sqrt(dx * dx + dy * dy);

            if (length === 0) return;

            // 计算垂直向量
            const perpX = -dy / length * thickness / 2;
            const perpY = dx / length * thickness / 2;

            // 线条的四个顶点
            const vertices = [
                x1 + perpX, y1 + perpY,
                x1 - perpX, y1 - perpY,
                x2 + perpX, y2 + perpY,
                x2 - perpX, y2 - perpY
            ];

            // 添加两个三角形
            this.vertices.push(
                vertices[0], vertices[1], vertices[2], vertices[3], vertices[4], vertices[5],
                vertices[4], vertices[5], vertices[2], vertices[3], vertices[6], vertices[7]
            );

            const rgba = this.hexToRgba(color);
            for (let i = 0; i < 6; i++) {
                this.colors.push(rgba.r, rgba.g, rgba.b, rgba.a);
            }
        }

        // 渲染所有几何体
        render() {
            if (!this.gl || this.vertices.length === 0) return;

            this.gl.useProgram(this.program);

            // 设置分辨率uniform
            this.gl.uniform2f(this.resolutionUniformLocation, this.canvas.width, this.canvas.height);

            // 绑定顶点数据
            this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.vertexBuffer);
            this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(this.vertices), this.gl.STATIC_DRAW);
            this.gl.enableVertexAttribArray(this.positionAttributeLocation);
            this.gl.vertexAttribPointer(this.positionAttributeLocation, 2, this.gl.FLOAT, false, 0, 0);

            // 绑定颜色数据
            this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.colorBuffer);
            this.gl.bufferData(this.gl.ARRAY_BUFFER, new Float32Array(this.colors), this.gl.STATIC_DRAW);
            this.gl.enableVertexAttribArray(this.colorAttributeLocation);
            this.gl.vertexAttribPointer(this.colorAttributeLocation, 4, this.gl.FLOAT, false, 0, 0);

            // 绘制
            this.gl.drawArrays(this.gl.TRIANGLES, 0, this.vertices.length / 2);
        }

        // 颜色转换辅助函数
        hexToRgba(hex, alpha = 0.8) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16) / 255,
                g: parseInt(result[2], 16) / 255,
                b: parseInt(result[3], 16) / 255,
                a: alpha
            } : { r: 1, g: 1, b: 1, a: alpha };
        }
        
        // WebGL优化的包围盒绘制 - 支持2D后备
        drawBoundingBox(player, color = '#00ff00') {
            if (!player) return;

            const camera = this.getActiveCamera();
            if (!camera) return;

            // 获取玩家模型的关键点
            const playerBounds = this.getPlayerBounds(player);
            if (!playerBounds) return;

            const cameraPos = camera.entity.getPosition();
            const distance = playerBounds.center.distance(cameraPos);

            // 转换所有关键点到屏幕坐标
            const screenBounds = {
                head: this.worldToScreen(playerBounds.head),
                foot: this.worldToScreen(playerBounds.foot),
                leftShoulder: this.worldToScreen(playerBounds.leftShoulder),
                rightShoulder: this.worldToScreen(playerBounds.rightShoulder),
                center: this.worldToScreen(playerBounds.center)
            };

            // 检查是否有有效的屏幕坐标
            if (!screenBounds.head || !screenBounds.foot || !screenBounds.center) {
                if (CONFIG.debug) console.log("[FGE] Failed to Get Player Screen Boundaries");
                return;
            }

            // 计算包围盒尺寸
            const boxHeight = Math.abs(screenBounds.head.y - screenBounds.foot.y);
            const shoulderWidth = screenBounds.leftShoulder && screenBounds.rightShoulder ?
                Math.abs(screenBounds.leftShoulder.x - screenBounds.rightShoulder.x) : boxHeight * 0.4;
            const boxWidth = Math.max(shoulderWidth, boxHeight * 0.3);

            // 动态调整最小尺寸
            const minSize = Math.max(20, 200 / distance);
            const finalWidth = Math.max(boxWidth, minSize);
            const finalHeight = Math.max(boxHeight, minSize * 2);

            const boxLeft = screenBounds.center.x - finalWidth / 2;
            const boxTop = screenBounds.head.y - 10;
            const lineWidth = Math.max(1, 3 - distance / 100);

            if (this.gl) {
                // WebGL渲染路径
                this.drawWebGLBox(boxLeft, boxTop, finalWidth, finalHeight, color, lineWidth);
                this.drawWebGLText(screenBounds.center.x, boxTop, `${Math.round(distance)}m`, color, distance);
            } else if (this.ctx) {
                // 2D Canvas后备路径
                this.draw2DBox(boxLeft, boxTop, finalWidth, finalHeight, color, lineWidth, screenBounds.center.x, distance);
            }

            // 调试信息
            if (CONFIG.debug) {
                console.log("[FGE] Bounding Box Info:", {
                    distance: distance.toFixed(1),
                    boxSize: `${finalWidth.toFixed(0)}x${finalHeight.toFixed(0)}`,
                    screenCenter: screenBounds.center,
                    renderer: this.gl ? 'WebGL' : '2D'
                });
            }
        }

        // WebGL盒子绘制
        drawWebGLBox(x, y, width, height, color, lineWidth) {
            // 绘制矩形边框（4条线）
            this.addLine(x, y, x + width, y, color, lineWidth); // 顶边
            this.addLine(x + width, y, x + width, y + height, color, lineWidth); // 右边
            this.addLine(x + width, y + height, x, y + height, color, lineWidth); // 底边
            this.addLine(x, y + height, x, y, color, lineWidth); // 左边
        }

        // WebGL文字绘制（使用2D Canvas覆盖层）
        drawWebGLText(x, y, text, color, distance) {
            // WebGL无法直接渲染文字，使用2D Canvas覆盖层
            if (!this.textCanvas) {
                this.createTextCanvas();
                if (CONFIG.debug) {
                    console.log("[FGE] Created text canvas overlay");
                }
            }

            const fontSize = Math.max(10, 14 - distance / 50);
            const textWidth = text.length * fontSize * 0.6;
            const textHeight = fontSize;

            if (CONFIG.debug) {
                console.log("[FGE] Drawing text:", {
                    text, x, y, color, fontSize,
                    canvasSize: `${this.textCanvas.width}x${this.textCanvas.height}`,
                    finalTextY: y - 6,
                    backgroundRect: {
                        x: x - textWidth/2 - 4,
                        y: y - textHeight - 8,
                        width: textWidth + 8,
                        height: textHeight + 4
                    }
                });
            }

            // 设置文字样式
            this.textCtx.save();
            this.textCtx.font = `${fontSize}px Arial, sans-serif`;
            this.textCtx.textAlign = 'center';
            this.textCtx.textBaseline = 'top';

            // 绘制文字背景 (与2D Canvas后备路径一致)
            // 有点干扰可读（视）性，先注释掉。
            // this.textCtx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            // this.textCtx.fillRect(
            //     x - textWidth/2 - 4,
            //     y - textHeight - 8,
            //     textWidth + 8,
            //     textHeight + 4
            // );

            // 绘制文字 (与2D Canvas后备路径一致)
            this.textCtx.fillStyle = color;
            this.textCtx.fillText(text, x, y - 14);

            this.textCtx.restore();
        }

        // 创建文字渲染的2D Canvas覆盖层
        createTextCanvas() {
            this.textCanvas = document.createElement('canvas');
            this.textCanvas.width = this.canvas.width;
            this.textCanvas.height = this.canvas.height;

            // 复制主canvas的样式
            this.textCanvas.style.position = 'fixed';
            this.textCanvas.style.top = '0';
            this.textCanvas.style.left = '0';
            this.textCanvas.style.width = '100%';
            this.textCanvas.style.height = '100%';
            this.textCanvas.style.pointerEvents = 'none';
            this.textCanvas.style.zIndex = '10001'; // 确保在WebGL canvas之上
            this.textCanvas.style.background = 'transparent';

            this.textCtx = this.textCanvas.getContext('2d');

            // 将文字canvas添加到body，确保它在最上层
            document.body.appendChild(this.textCanvas);

            if (CONFIG.debug) {
                console.log("[FGE] Text canvas created and added to DOM:", {
                    width: this.textCanvas.width,
                    height: this.textCanvas.height,
                    style: this.textCanvas.style.cssText,
                    parent: this.textCanvas.parentNode.tagName
                });
            }
        }

        // 清除所有文字
        clearText() {
            if (this.textCtx) {
                this.textCtx.clearRect(0, 0, this.textCanvas.width, this.textCanvas.height);
            }
        }

        // 2D Canvas后备绘制
        draw2DBox(boxLeft, boxTop, finalWidth, finalHeight, color, lineWidth, centerX, distance) {
            this.ctx.save();

            // 设置矩形框样式
            this.ctx.strokeStyle = color;
            this.ctx.lineWidth = lineWidth;
            this.ctx.globalAlpha = 0.8;

            // 添加发光效果
            this.ctx.shadowColor = color;
            this.ctx.shadowBlur = 5;

            // 绘制主包围盒
            this.ctx.strokeRect(boxLeft, boxTop, finalWidth, finalHeight);

            // 重置阴影效果
            this.ctx.shadowColor = 'transparent';
            this.ctx.shadowBlur = 0;

            // 绘制距离指示器
            const fontSize = Math.max(10, 14 - distance / 50);
            this.ctx.font = `${fontSize}px Arial, sans-serif`;
            this.ctx.fillStyle = color;
            this.ctx.textAlign = 'center';

            const distanceText = `${Math.round(distance)}m`;
            const textMetrics = this.ctx.measureText(distanceText);
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            // 绘制文字背景
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(
                centerX - textWidth/2 - 4,
                boxTop - textHeight - 8,
                textWidth + 8,
                textHeight + 4
            );

            // 绘制距离文字
            this.ctx.fillStyle = color;
            this.ctx.fillText(distanceText, centerX, boxTop - 6);

            this.ctx.restore();
        }
        
        // 获取玩家模型的精确边界点
        getPlayerBounds(player) {
            try {
                const playerPos = player.getPosition();
                
                // 尝试获取玩家的实际模型节点
                const modelNode = this.findPlayerModel(player);
                let playerHeight = 1.8; // 默认玩家高度
                let playerWidth = 0.6;  // 默认玩家宽度
                
                if (modelNode) {
                    // 如果找到模型，尝试获取准确尺寸
                    const bounds = this.getModelBounds(modelNode);
                    if (bounds) {
                        playerHeight = bounds.height;
                        playerWidth = bounds.width;
                    }
                }
                
                // 计算关键点的世界坐标
                return {
                    center: playerPos.clone(),
                    foot: new window.pc.Vec3(playerPos.x, playerPos.y - playerHeight / 2, playerPos.z),
                    head: new window.pc.Vec3(playerPos.x, playerPos.y + playerHeight / 2, playerPos.z),
                    leftShoulder: new window.pc.Vec3(
                        playerPos.x - playerWidth / 2, 
                        playerPos.y + playerHeight * 0.3, 
                        playerPos.z
                    ),
                    rightShoulder: new window.pc.Vec3(
                        playerPos.x + playerWidth / 2, 
                        playerPos.y + playerHeight * 0.3, 
                        playerPos.z
                    )
                };
                
            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] Get Player Bounds Failed:", e);
                return null;
            }
        }
        
        // 查找玩家模型节点
        findPlayerModel(player) {
            try {
                // 常见的模型节点名称
                const modelNames = ['Model', 'PlayerModel', 'Character', 'Body', 'Mesh'];
                
                for (let modelName of modelNames) {
                    const modelNode = player.findByName(modelName);
                    if (modelNode) {
                        return modelNode;
                    }
                }
                
                // 查找带有render组件的子节点
                const findRenderNode = (node) => {
                    if (node.render && node.render.enabled) {
                        return node;
                    }
                    for (let child of node.children) {
                        const result = findRenderNode(child);
                        if (result) return result;
                    }
                    return null;
                };
                
                return findRenderNode(player);
                
            } catch (e) {
                return null;
            }
        }
        
        // 获取模型边界信息
        getModelBounds(modelNode) {
            try {
                if (modelNode.render && modelNode.render.meshInstances) {
                    const meshInstances = modelNode.render.meshInstances;
                    if (meshInstances.length > 0) {
                        const aabb = meshInstances[0].aabb;
                        if (aabb) {
                            return {
                                width: aabb.halfExtents.x * 2,
                                height: aabb.halfExtents.y * 2,
                                depth: aabb.halfExtents.z * 2
                            };
                        }
                    }
                }
                return null;
            } catch (e) {
                return null;
            }
        }
        
        // 重写的坐标转换系统 - 核心修复
        worldToScreen(worldPos) {
            try {
                const camera = this.getActiveCamera();
                if (!camera) {
                    if (CONFIG.debug) console.log("[FGE] No Camera Found");
                    return null;
                }
                
                // 方法1: 使用PlayCanvas内置转换
                if (camera.worldToScreen) {
                    try {
                        const screenPos = camera.worldToScreen(worldPos);
                        
                        // 检查是否在相机前方
                        if (screenPos.z <= 0) {
                            if (CONFIG.debug) console.log("[FGE] Target Behind Camera");
                            return null;
                        }
                        
                        // 转换到实际屏幕坐标
                        const finalPos = {
                            x: screenPos.x * window.innerWidth,
                            y: (1 - screenPos.y) * window.innerHeight
                        };
                        
                        // 验证坐标合理性
                        if (this.isValidScreenCoord(finalPos)) {
                            return finalPos;
                        }
                    } catch (e) {
                        if (CONFIG.debug) console.log("[FGE] Built-in worldToScreen Failed:", e);
                    }
                }
                
                // 方法2: 手动计算投影
                return this.calculateProjection(worldPos, camera);
                
            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] World to Screen Conversion Error:", e);
                return null;
            }
        }
        
        // 改进的手动投影计算 - 增强透视效果
        calculateProjection(worldPos, camera) {
            try {
                const cameraEntity = camera.entity;
                const cameraPos = cameraEntity.getPosition();
                const cameraRot = cameraEntity.getRotation();
                
                // 计算相对位置向量
                const relativePos = worldPos.clone().sub(cameraPos);
                
                // 获取相机的方向向量
                const forward = new window.pc.Vec3(0, 0, -1);
                const right = new window.pc.Vec3(1, 0, 0);
                const up = new window.pc.Vec3(0, 1, 0);
                
                // 应用相机旋转
                cameraRot.transformVector(forward, forward);
                cameraRot.transformVector(right, right);
                cameraRot.transformVector(up, up);
                
                // 计算深度（Z方向）
                const depth = relativePos.dot(forward);
                if (depth <= 0.1) {
                    if (CONFIG.debug) console.log("[FGE] Target Too Close or Behind:", depth);
                    return null;
                }
                
                // 计算水平和垂直偏移
                const horizontalOffset = relativePos.dot(right);
                const verticalOffset = relativePos.dot(up);
                
                // 改进的透视投影计算
                const fov = camera.fov || 45;
                const fovRad = fov * Math.PI / 180;
                const aspectRatio = window.innerWidth / window.innerHeight;
                
                // 使用更精确的投影公式
                const tanHalfFov = Math.tan(fovRad / 2);
                const projectionHeight = tanHalfFov * depth;
                const projectionWidth = projectionHeight * aspectRatio;
                
                // 归一化坐标
                const normalizedX = horizontalOffset / projectionWidth;
                const normalizedY = verticalOffset / projectionHeight;
                
                // 转换到屏幕坐标，确保正确的透视缩放
                const screenX = window.innerWidth * 0.5 + normalizedX * window.innerWidth * 0.5;
                const screenY = window.innerHeight * 0.5 - normalizedY * window.innerHeight * 0.5;
                
                const result = { 
                    x: screenX, 
                    y: screenY,
                    distance: depth // 添加距离信息用于缩放
                };
                
                if (CONFIG.debug) {
                    console.log("[FGE] Improved Projection Calculation:", {
                        worldPos: worldPos.toString(),
                        depth: depth.toFixed(2),
                        screenPos: `(${screenX.toFixed(0)}, ${screenY.toFixed(0)})`,
                        fov: fov
                    });
                }
                
                return this.isValidScreenCoord(result) ? result : null;
                
            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] Manual Projection Calculation Failed:", e);
                return null;
            }
        }
        
        // 改进的相机获取逻辑
        getActiveCamera() {
            try {
                if (!window.pc || !window.pc.app || !window.pc.app.root) return null;
                
                // 方法1: 查找玩家相机
                const gameNode = window.pc.app.root.findByName("Game");
                if (gameNode) {
                    const playerNode = gameNode.findByName("Player");
                    if (playerNode) {
                        // 尝试多种可能的相机路径
                        const cameraPaths = [
                            "LookHolder/Head/Lens",
                            "Head/Camera",
                            "Camera",
                            "FPSCamera",
                            "MainCamera"
                        ];
                        
                        for (let path of cameraPaths) {
                            const cameraNode = this.findNodeByPath(playerNode, path);
                            if (cameraNode && cameraNode.camera) {
                                if (CONFIG.debug) console.log("[FGE] Found Player Camera:", path);
                                return cameraNode.camera;
                            }
                        }
                    }
                }
                
                // 方法2: 查找所有相机组件
                const cameras = window.pc.app.root.findComponents('camera');
                for (let camera of cameras) {
                    if (camera.enabled && camera.entity.enabled) {
                        if (CONFIG.debug) console.log("[FGE] Using Generic Camera");
                        return camera;
                    }
                }
                
                if (CONFIG.debug) console.log("[FGE] No Camera Found");
                return null;
                
            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] Get Camera Error:", e);
                return null;
            }
        }
        
        // 辅助函数：按路径查找节点
        findNodeByPath(rootNode, path) {
            const parts = path.split('/');
            let currentNode = rootNode;
            
            for (let part of parts) {
                currentNode = currentNode.findByName(part);
                if (!currentNode) return null;
            }
            
            return currentNode;
        }
        
        // 验证屏幕坐标有效性
        isValidScreenCoord(coord) {
            const margin = 200; // 允许一定的屏幕外边距
            return coord.x >= -margin && 
                   coord.x <= window.innerWidth + margin &&
                   coord.y >= -margin && 
                   coord.y <= window.innerHeight + margin;
        }
        
        destroy() {
            // 恢复原始方法以避免检测
            if (this.originalGetContext) {
                HTMLCanvasElement.prototype.getContext = this.originalGetContext;
            }

            // 清理文字canvas
            if (this.textCanvas) {
                this.textCanvas.remove();
                this.textCanvas = null;
                this.textCtx = null;
            }

            if (this.canvas) {
                this.canvas.remove();
                this.canvas = null;
                this.gl = null;
                this.ctx = null;
            }
            this.isInitialized = false;
        }

        // 完成渲染（WebGL需要显式调用）
        finishRender() {
            if (this.gl) {
                this.render();
            }
        }
    }
    
    // 优化的UI系统 - 改进默认显示
    class StealthUI {
        constructor() {
            this.uiElement = null;
            this.isVisible = true; // 默认为显示状态
            this.autoHideTimer = null; // 自动隐藏定时器
            this.autoHideDelay = CONFIG.autoHideDelay; // 从配置读取延迟时间
            this.countdownTimer = null; // 倒计时更新定时器
            this.remainingTime = 0; // 剩余时间
        }
        
        create() {
            this.uiElement = document.createElement('div');
            this.uiElement.style.cssText = `
                position: fixed;
                top: 15px;
                right: 15px;
                background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 20, 0.95));
                color: #00ff88;
                padding: 15px 20px;
                border-radius: 12px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 13px;
                z-index: 2000;
                user-select: none;
                border: 2px solid #00ff88;
                box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3), inset 0 1px 0 rgba(255,255,255,0.1);
                backdrop-filter: blur(5px);
                min-width: 180px;
                display: block;
                transition: all 0.3s ease;
                transform: translateX(0);
                opacity: 1;
            `;
            
            // 初始显示等待状态的内容
            this.showInitialStatus();
            
            document.body.appendChild(this.uiElement);
        }
        
        // 显示初始状态（游戏引擎未就绪时）
        showInitialStatus() {
            if (!this.uiElement) return;

            // 启动自动隐藏定时器
            this.startAutoHideTimer();
            
            this.uiElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: #ffa500; margin-right: 8px; box-shadow: 0 0 6px #ffa500; animation: pulse 1.5s infinite;"></div>
                    <span style="font-weight: bold; color: #ffa500;">ESP Initializing</span>
                </div>
                <div style="border-left: 2px solid #ffa500; padding-left: 10px; margin-left: 4px;">
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Status:</span> 
                        <span style="color: #fff; font-weight: bold;">Waiting Game Engine</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span style="color: #888;">Version:</span> 
                        <span style="color: #fff; font-weight: bold;">Venge ESP v3.0 WebGL</span>
                    </div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div>Ctrl+H: E/D Panel</div>
                    <div>Ctrl+X: E/D ESP</div>
                    <div>Ctrl+A: E/D Aimbot</div>
                    ${CONFIG.autoHideUI ? `<div style="color: #ffa500;">Auto-hide: ${CONFIG.autoHideDelay/1000}s</div>` : ''}
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div style="text-align:center;">V3.0 WebGL FEG © 2025</div>
                </div>
                <style>
                    @keyframes pulse {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0.5; }
                    }
                </style>
            `;
        }
        
        // 显示就绪但未开启状态
        showReadyStatus() {
            if (!this.uiElement) return;

            // 启动自动隐藏定时器
            this.startAutoHideTimer();
            
            this.uiElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: #ff6b6b; margin-right: 8px; box-shadow: 0 0 6px #ff6b6b;"></div>
                    <span style="font-weight: bold; color: #ff6b6b;">ESP Waiting</span>
                </div>
                <div style="border-left: 2px solid #ff6b6b; padding-left: 10px; margin-left: 4px;">
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Status:</span> 
                        <span style="color: #fff; font-weight: bold;">Ready</span>
                    </div>
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Target Count:</span> 
                        <span style="color: #fff; font-weight: bold;">-</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span style="color: #888;">Closest Distance:</span> 
                        <span style="color: #fff; font-weight: bold;">-</span>
                    </div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div>Ctrl+H: E/D Panel</div>
                    <div style="color: #ffa500;">Ctrl+X: E/D ESP</div>
                    <div>Ctrl+A: E/D Aimbot</div>
                    ${CONFIG.autoHideUI ? `<div style="color: #ffa500;">Auto-hide: ${CONFIG.autoHideDelay/1000}s</div>` : ''}
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div style="text-align:center;">V3.0 WebGL FEG © 2025</div>
                </div>
            `;
        }
        
        show() {
            if (this.uiElement) {
                this.uiElement.style.display = 'block';
                this.isVisible = true;
                // 添加显示动画
                setTimeout(() => {
                    this.uiElement.style.transform = 'translateX(0)';
                    this.uiElement.style.opacity = '1';
                }, 10);

                // 启动自动隐藏定时器
                this.startAutoHideTimer();
            }
        }
        
        hide() {
            if (this.uiElement) {
                this.uiElement.style.display = 'none';
                this.isVisible = false;

                // 移除倒计时元素
                const countdownElement = this.uiElement.querySelector('.auto-hide-countdown');
                if (countdownElement) {
                    countdownElement.remove();
                }
            }
            // 清除自动隐藏定时器
            this.clearAutoHideTimer();
        }

        // 启动自动隐藏定时器
        startAutoHideTimer() {
            // 检查是否启用自动隐藏功能
            if (!CONFIG.autoHideUI) return;

            // 清除之前的定时器
            this.clearAutoHideTimer();

            // 初始化倒计时
            this.remainingTime = this.autoHideDelay / 1000;

            // 启动倒计时更新
            this.startCountdown();

            // 设置新的定时器
            this.autoHideTimer = setTimeout(() => {
                if (this.isVisible) {
                    this.hide();
                    console.log(`[FGE] UI Panel auto-hidden after ${this.autoHideDelay/1000} seconds`);
                }
            }, this.autoHideDelay);
        }

        // 清除自动隐藏定时器
        clearAutoHideTimer() {
            if (this.autoHideTimer) {
                clearTimeout(this.autoHideTimer);
                this.autoHideTimer = null;
            }
            this.clearCountdown();
        }

        // 启动倒计时显示
        startCountdown() {
            this.clearCountdown();
            this.countdownTimer = setInterval(() => {
                this.remainingTime -= 1;
                this.updateCountdownDisplay();

                if (this.remainingTime <= 0) {
                    this.clearCountdown();
                }
            }, 1000);
        }

        // 清除倒计时
        clearCountdown() {
            if (this.countdownTimer) {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
            }
            this.remainingTime = 0;
        }

        // 更新倒计时显示
        updateCountdownDisplay() {
            if (!this.uiElement || !CONFIG.autoHideUI || this.remainingTime <= 0) return;

            // 查找或创建倒计时元素
            let countdownElement = this.uiElement.querySelector('.auto-hide-countdown');
            if (!countdownElement) {
                countdownElement = document.createElement('div');
                countdownElement.className = 'auto-hide-countdown';
                countdownElement.style.cssText = `
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    background: rgba(255, 107, 107, 0.8);
                    color: white;
                    padding: 2px 6px;
                    border-radius: 8px;
                    font-size: 10px;
                    font-weight: bold;
                `;
                this.uiElement.appendChild(countdownElement);
            }

            countdownElement.textContent = `${Math.ceil(this.remainingTime)}s`;
        }
        
        toggle() {
            if (this.isVisible) {
                this.hide();
            } else {
                this.show();
            }
        }

        // 重置自动隐藏定时器（用于用户交互时延长显示时间）
        resetAutoHideTimer() {
            if (this.isVisible) {
                this.startAutoHideTimer();
            }
        }
        
        updateUI(_, targetCount, closestDistance, espActive, aimbotActive = false) {
            if (!this.uiElement || !this.isVisible) return;

            const statusColor = espActive ? '#00ff88' : '#ff6b6b';
            const displayStatus = espActive ? 'ACTIVE' : 'DISABLED';
            const aimbotColor = aimbotActive ? '#ffa500' : '#666';
            const aimbotStatus = aimbotActive ? 'ON' : 'OFF';

            // 重置自动隐藏定时器，让用户有时间看到更新
            this.resetAutoHideTimer();

            this.uiElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: ${statusColor}; margin-right: 8px; box-shadow: 0 0 6px ${statusColor};"></div>
                    <span style="font-weight: bold; color: ${statusColor};">ESP ${displayStatus}</span>
                </div>
                <div style="border-left: 2px solid #00ff88; padding-left: 10px; margin-left: 4px;">
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Target Count:</span>
                        <span style="color: #fff; font-weight: bold;">${targetCount}</span>
                    </div>
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Closest Distance:</span>
                        <span style="color: #fff; font-weight: bold;">${closestDistance}m</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span style="color: #888;">Aimbot:</span>
                        <span style="color: ${aimbotColor}; font-weight: bold;">${aimbotStatus}</span>
                    </div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div>Ctrl+H: E/D Panel</div>
                    <div>Ctrl+X: E/D ESP</div>
                    <div style="color: ${aimbotColor};">Ctrl+A: E/D Aimbot</div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div style="text-align:center;">V3.0 WebGL FEG © 2025</div>
                </div>
            `;
        }
        
        // 显示引擎错误状态
        showEngineError() {
            if (!this.uiElement || !this.isVisible) return;
            
            this.uiElement.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 8px;">
                    <div style="width: 8px; height: 8px; border-radius: 50%; background: #ff4757; margin-right: 8px; box-shadow: 0 0 6px #ff4757; animation: pulse 1s infinite;"></div>
                    <span style="font-weight: bold; color: #ff4757;">Engine Error</span>
                </div>
                <div style="border-left: 2px solid #ff4757; padding-left: 10px; margin-left: 4px;">
                    <div style="margin-bottom: 4px;">
                        <span style="color: #888;">Status:</span> 
                        <span style="color: #ff4757; font-weight: bold;">Cannot Get Game Data</span>
                    </div>
                    <div style="margin-bottom: 8px;">
                        <span style="color: #888;">Suggest:</span> 
                        <span style="color: #fff; font-weight: bold;">Reload Page</span>
                    </div>
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div>Ctrl+H: E/D Panel</div>
                    <div>Ctrl+X: E/D ESP</div>
                    ${CONFIG.autoHideUI ? `<div style="color: #888;">Auto-hide: ${CONFIG.autoHideDelay/1000}s</div>` : ''}
                </div>
                <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px; font-size: 11px; color: #666;">
                    <div style="text-align:center;">V3.0 WebGL FEG © 2025</div>
                </div>
                <style>
                    @keyframes pulse {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0.3; }
                    }
                </style>
            `;
        }
        
        destroy() {
            if (this.uiElement) {
                this.uiElement.remove();
                this.uiElement = null;
            }
        }
    }

    // 自瞄系统 - 参考esp-first.js实现
    class AimbotSystem {
        constructor() {
            this.isEnabled = false;
            this.originalSetShooting = null;
            this.lastTargetTime = 0;
            this.targetCooldown = 100; // 目标切换冷却时间(ms)
        }

        enable() {
            if (this.isEnabled) return;

            try {
                // 保存原始射击函数
                if (window.pc && window.pc.controls && window.pc.controls.setShooting) {
                    this.originalSetShooting = window.pc.controls.setShooting;

                    // 替换为自瞄版本
                    window.pc.controls.setShooting = this.createAimbotShooting();
                    this.isEnabled = true;

                    console.log("[FGE] Aimbot Enabled");
                    return true;
                }
            } catch (e) {
                console.error("[FGE] Aimbot Enable Failed:", e);
            }
            return false;
        }

        disable() {
            if (!this.isEnabled) return;

            try {
                // 恢复原始射击函数
                if (this.originalSetShooting && window.pc && window.pc.controls) {
                    window.pc.controls.setShooting = this.originalSetShooting;
                    this.isEnabled = false;

                    console.log("[FGE] Aimbot Disabled");
                    return true;
                }
            } catch (e) {
                console.error("[FGE] Aimbot Disable Failed:", e);
            }
            return false;
        }

        // 创建自瞄射击函数 - 基于esp-first.js的实现
        createAimbotShooting() {
            const self = this;

            return function(t) {
                if (!this.isMouseLocked) return false;

                // 查找最近的敌人目标
                const target = self.findClosestTarget();

                if ("Melee" == this.currentWeapon.type && this.setMeleeShoot(),
                "Throwable" == this.currentWeapon.type && this.setThrowShoot(),
                "Launcher" == this.currentWeapon.type && this.setLauncherShoot(t),
                this.player.checkShooting(),
                !this.currentWeapon.isShootable)
                    return false;

                if (this.leftMouse || this.isShootingLocked || this.isFireStopped || (this.stopFiring(),
                0 === this.currentWeapon.ammo && this.reload()),
                this.leftMouse && !this.isWeaponHidden && !this.isShootingLocked && !this.playerAbilities.isThrowing && this.isReloading < this.timestamp && this.playerAbilities.isHitting < this.timestamp && (this.currentWeapon.ammo > 0 ? this.isShooting = this.currentWeapon.shootTime + this.timestamp : this.reload()),
                this.isShooting > this.timestamp && !this.isShootingLocked) {

                    this.currentWeapon.recoil,
                    this.currentWeapon.cameraShake,
                    Math.random(),
                    Math.random(),
                    this.currentWeapon.spread;

                    var i = Math.cos(110 * this.spreadCount);
                    this.currentWeapon.spread;
                    this.cancelInspect(!0),
                    this.isFocusing && "Rifle" == this.currentWeapon.type && (-.05,
                    .5,
                    -.2,
                    .5,
                    .05,
                    this.currentWeapon.focusSpread,
                    this.currentWeapon.focusSpread * i),
                    "Sniper" != this.currentWeapon.type && "Shotgun" != this.currentWeapon.type || (this.spreadNumber = this.currentWeapon.spread,
                    this.isFocusing && (this.spreadNumber = this.currentWeapon.focusSpread),
                    -5,
                    5.2),
                    this.currentWeapon.shoot(),
                    this.currentWeapon.isAutomatic || (this.isMouseReleased = !1,
                    this.leftMouse = !1);

                    var e = this.currentWeapon.bulletPoint.getPosition().clone()
                      , s = this.currentWeapon.bulletPoint.getEulerAngles().clone();
                    "Sniper" == this.currentWeapon.type && this.isFocusing || (this.app.fire("EffectManager:Bullet", e, s),
                    this.entity.script.weaponManager.triggerShooting());

                    var o = this.currentWeapon.muzzlePoint.getPosition().clone()
                      , n = this.raycastShootFrom
                      , a = Math.random() * this.spreadNumber - Math.random() * this.spreadNumber
                      , h = Math.random() * this.spreadNumber - Math.random() * this.spreadNumber
                      , r = Math.random() * this.spreadNumber - Math.random() * this.spreadNumber;

                    // 自瞄逻辑：如果有目标，瞄准目标；否则使用原始瞄准点
                    var p;
                    if (target && CONFIG.enableAimbot) {
                        // 计算瞄准点（头部或身体中心）
                        const aimY = CONFIG.aimHeadshot ?
                            target.position.y + 1.5 + Math.random()/10 :
                            target.position.y + Math.random()/10;
                        p = new window.pc.Vec3(target.position.x, aimY, target.position.z);
                    } else {
                        // 使用原始瞄准点
                        p = this.raycastTo.clone().add(new window.pc.Vec3(a, h, r));
                    }

                    var c = this.currentWeapon.damage
                      , m = this.currentWeapon.distanceMultiplier;

                    if ("Shotgun" == this.currentWeapon.type) {
                        this.app.fire("EffectManager:Fire", n, p, o, this.player.playerId, c, "Shotgun", m);
                        for (var u = 1, l = 0; l < 10; l++)
                            l > 5 && (u = .5),
                            a = Math.cos(l / 3 * Math.PI) * this.spreadNumber * u,
                            h = Math.sin(l / 3 * Math.PI) * this.spreadNumber * u,
                            r = Math.cos(l / 3 * Math.PI) * this.spreadNumber * u,
                            // 对霰弹枪的每一发子弹也应用自瞄
                            p = target && CONFIG.enableAimbot ?
                                new window.pc.Vec3(target.position.x, target.position.y + 2 + Math.random()/10, target.position.z) :
                                this.raycastTo.clone().add(new window.pc.Vec3(a, h, r)),
                            this.app.fire("EffectManager:Fire", n, p, o, this.player.playerId, c, "Shotgun", m)
                    } else {
                        this.app.fire("EffectManager:Fire", n, p, o, this.player.playerId, c);
                    }

                    this.setShakeAnimation(t),
                    this.isShootingLocked = !0,
                    this.isFireStopped = !1
                }

                this.isShooting < this.timestamp && this.isShootingLocked && (this.isShootingLocked = !1),
                this.updateShakeAnimation(t)
            };
        }

        // 查找最近的有效目标
        findClosestTarget() {
            try {
                if (!window.pc || !window.pc.app || !window.pc.app.root) return null;

                const gameNode = window.pc.app.root.findByName("Game");
                if (!gameNode) return null;

                const currentPlayer = gameNode.findByName("Player");
                const playerHolder = gameNode.findByName("PlayerHolder");

                if (!currentPlayer || !playerHolder) return null;

                const currentPlayerPos = currentPlayer.getPosition();
                let closestDistance = Infinity;
                let closestTarget = null;

                // 遍历所有玩家寻找最近的敌人
                for (let i = 2; i < playerHolder.children.length; i++) {
                    const player = playerHolder.children[i];

                    if (!player || !player.enabled) continue;
                    if (!player.script || !player.script.scripts || !player.script.scripts[0]) continue;

                    const playerScript = player.script.scripts[0];

                    // 检查是否为有效目标
                    if (playerScript.health <= 0) continue;

                    // 检查队伍（如果不是FFA模式）
                    if (window.pc.currentTeam &&
                        playerScript.team === window.pc.currentTeam &&
                        playerScript.team !== "none") {
                        continue; // 跳过队友
                    }

                    const distance = player.position.distance(currentPlayerPos);

                    // 检查距离限制
                    if (distance > CONFIG.aimMaxDistance) continue;

                    // 找到更近的目标
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestTarget = player;
                    }
                }

                return closestTarget;

            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] Find Target Error:", e);
                return null;
            }
        }
    }

    // WebGL增强的主ESP系统 - 改进状态管理和检测规避
    class AdvancedESP {
        constructor() {
            this.isActive = false;
            this.updateInterval = null;
            this.renderer = new WebGLRenderer();
            this.ui = new StealthUI();
            this.lastUpdateTime = 0;

            // 自瞄系统相关
            this.aimbot = new AimbotSystem();
            this.originalSetShooting = null;
        }
        
        init() {
            setTimeout(() => {
                this.renderer.init();
                this.ui.create();
                this.bindControls();
                console.log("[FGE] ESP System Initialized");
                
                // 根据游戏引擎状态更新UI
                this.updateUIStatus();
            }, 1000);
        }
        
        // 更新UI状态
        updateUIStatus() {
            if (!gameEngineReady) {
                this.ui.showInitialStatus();
            } else if (!this.isActive) {
                this.ui.showReadyStatus();
            }
        }
        
        bindControls() {
            document.addEventListener('keydown', (event) => {
                // Ctrl+H: 切换UI面板显示/隐藏
                if (event.code === 'KeyH' && event.ctrlKey && !event.shiftKey) {
                    event.preventDefault();
                    this.ui.toggle();
                }

                // Ctrl+X: 切换ESP开关
                if (event.code === 'KeyX' && event.ctrlKey && !event.shiftKey) {
                    event.preventDefault();
                    this.toggle();
                }

                // Ctrl+A: 切换自瞄开关
                if (event.code === 'KeyA' && event.ctrlKey && !event.shiftKey) {
                    event.preventDefault();
                    this.toggleAimbot();
                }
            });
        }
        
        toggle() {
            if (!gameEngineReady) {
                console.log("[FGE] Game Engine Not Ready");
                // 即使引擎未就绪，也更新UI显示当前状态
                this.ui.showInitialStatus();
                return;
            }
            
            if (this.isActive) {
                this.disable();
            } else {
                this.enable();
            }
        }

        // 切换自瞄功能
        toggleAimbot() {
            if (!gameEngineReady) {
                console.log("[FGE] Game engine not ready, cannot toggle aimbot");
                return;
            }

            CONFIG.enableAimbot = !CONFIG.enableAimbot;

            if (CONFIG.enableAimbot) {
                if (this.aimbot.enable()) {
                    console.log("[FGE] Aimbot Enabled");
                    this.showAimbotStatus(true);
                } else {
                    CONFIG.enableAimbot = false;
                    console.log("[FGE] Failed to enable aimbot");
                }
            } else {
                if (this.aimbot.disable()) {
                    console.log("[FGE] Aimbot Disabled");
                    this.showAimbotStatus(false);
                }
            }
        }

        // 显示自瞄状态提示
        showAimbotStatus(enabled) {
            // 创建临时状态提示
            const statusDiv = document.createElement('div');
            statusDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: ${enabled ? 'rgba(0, 255, 136, 0.9)' : 'rgba(255, 71, 87, 0.9)'};
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                font-family: 'Consolas', monospace;
                font-size: 16px;
                font-weight: bold;
                z-index: 10000;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                border: 2px solid ${enabled ? '#00ff88' : '#ff4757'};
            `;
            statusDiv.textContent = `Aimbot ${enabled ? 'ENABLED' : 'DISABLED'}`;

            document.body.appendChild(statusDiv);

            // 2秒后自动移除
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.remove();
                }
            }, 2000);
        }

        enable() {
            this.isActive = true;
            this.startUpdateLoop();
            console.log("[FGE] ESP Enabled");
        }
        
        disable() {
            this.isActive = false;

            if (this.updateInterval) {
                cancelAnimationFrame(this.updateInterval);
                this.updateInterval = null;
            }

            // 禁用自瞄
            if (CONFIG.enableAimbot) {
                this.aimbot.disable();
                CONFIG.enableAimbot = false;
            }

            this.renderer.clear();
            // 更新UI显示就绪状态
            this.ui.showReadyStatus();
            console.log("[FGE] ESP Disabled");
        }
        
        startUpdateLoop() {
            const update = (timestamp) => {
                if (!this.isActive) return;
                
                if (timestamp - this.lastUpdateTime >= CONFIG.updateInterval) {
                    this.updateESP();
                    this.lastUpdateTime = timestamp;
                }
                
                this.updateInterval = requestAnimationFrame(update);
            };
            
            this.updateInterval = requestAnimationFrame(update);
        }
        
        // 核心ESP更新逻辑 - 仅保留包围盒
        updateESP() {
            try {
                const gameData = this.getGameData();
                if (!gameData) {
                    this.ui.updateUI('ENGINE ERROR', 0, 0, false, false);
                    return;
                }
                
                this.renderer.clear();
                
                let validTargets = 0;
                let closestDistance = Infinity;
                const currentPlayerPos = gameData.currentPlayer.getPosition();
                
                // 遍历所有其他玩家
                for (let player of gameData.otherPlayers) {
                    try {
                        if (!this.isValidTarget(player)) continue;
                        
                        const playerPos = player.getPosition();
                        const distance = playerPos.distance(currentPlayerPos);
                        
                        if (distance > CONFIG.maxDrawDistance) continue;
                        
                        validTargets++;
                        closestDistance = Math.min(closestDistance, distance);
                        
                        // 获取玩家信息
                        const playerInfo = this.getPlayerInfo(player);
                        const color = playerInfo.isTeammate ? '#00ff88' : '#ff4757';
                        
                        if (CONFIG.debug) {
                            console.log(`[FGE] Draw Player ${playerInfo.name}:`, {
                                position: playerPos.toString(),
                                distance: distance.toFixed(1),
                                isTeammate: playerInfo.isTeammate
                            });
                        }
                        
                        // 仅绘制包围盒（矩形框 + 距离显示）
                        if (CONFIG.enableBoxESP) {
                            this.renderer.drawBoundingBox(player, color);
                        }
                        
                    } catch (e) {
                        if (CONFIG.debug) console.log("[FGE] Process Single Player Error:", e);
                    }
                }

                // 完成WebGL渲染
                this.renderer.finishRender();

                // 更新UI
                this.ui.updateUI(
                    'ACTIVE',
                    validTargets,
                    closestDistance === Infinity ? 0 : Math.round(closestDistance),
                    this.isActive,
                    CONFIG.enableAimbot
                );
                
            } catch (error) {
                if (CONFIG.debug) console.log("[FGE] ESP Update Error:", error);
                this.ui.updateUI('UPDATE ERROR', 0, 0, false, false);
            }
        }
        
        // 获取游戏数据的统一接口
        getGameData() {
            try {
                if (!window.pc || !window.pc.app || !window.pc.app.root) return null;
                
                const gameNode = window.pc.app.root.findByName("Game");
                if (!gameNode) return null;
                
                const currentPlayer = gameNode.findByName("Player");
                const playerHolder = gameNode.findByName("PlayerHolder");
                
                if (!currentPlayer || !playerHolder) return null;
                
                // 获取其他玩家列表 - 跳过第一位占位符玩家（索引0和1）
                const otherPlayers = [];
                for (let i = 2; i < playerHolder.children.length; i++) {
                    const child = playerHolder.children[i];
                    if (child !== currentPlayer && child.enabled) {
                        otherPlayers.push(child);
                    }
                }

                if (CONFIG.debug && otherPlayers.length > 0) {
                    console.log(`[FGE] Found ${otherPlayers.length} other players (skipped first 2 placeholder slots)`);
                }
                
                return {
                    currentPlayer,
                    otherPlayers,
                    gameNode
                };
                
            } catch (e) {
                if (CONFIG.debug) console.log("[FGE] Get Game Data Error:", e);
                return null;
            }
        }
        
        // 验证目标有效性
        isValidTarget(player) {
            try {
                if (!player || !player.enabled) return false;
                
                // 检查脚本组件
                if (!player.script || !player.script.scripts || !player.script.scripts[0]) {
                    return false;
                }
                
                const playerScript = player.script.scripts[0];
                
                // 检查生命值
                return playerScript.health > 0;
                
            } catch (e) {
                return false;
            }
        }
        
        // 获取玩家信息
        getPlayerInfo(player) {
            try {
                const playerScript = player.script.scripts[0];
                
                return {
                    name: playerScript.username || 'Unknown',
                    health: playerScript.health || 0,
                    team: playerScript.team || null,
                    isTeammate: false // 先设为false，后续可以实现队伍检测
                };
                
            } catch (e) {
                return {
                    name: 'Unknown',
                    health: 0,
                    team: null,
                    isTeammate: false
                };
            }
        }
        
        destroy() {
            this.disable();
            // 确保自瞄被完全禁用
            this.aimbot.disable();
            this.renderer.destroy();
            this.ui.destroy();
        }
    }
    
    // 引擎监控
    function checkGameEngine() {
        try {
            if (window.pc && window.pc.app && window.pc.app.root) {
                const gameNode = window.pc.app.root.findByName("Game");
                if (gameNode) {
                    if (!gameEngineReady) {
                        gameEngineReady = true;
                        console.log("[FGE] Game Engine Ready");
                        setTimeout(initializeESP, 500);
                    }
                    return;
                }
            }
        } catch (e) {
            if (CONFIG.debug) console.log("[FGE] Game Engine Check Error:", e);
        }
        
        if (gameEngineReady) {
            gameEngineReady = false;
            console.log("[FGE] Game Engine Exited");
        }
    }
    
    function initializeESP() {
        if (ESPInstance) {
            ESPInstance.destroy();
        }
        
        ESPInstance = new AdvancedESP();
        ESPInstance.init();
    }
    
    // 启动
    console.log("[FGE] Venge ESP Loading...");
    console.log("[FGE] New Features: Aimbot System Added");
    console.log("[FGE] Controls: Ctrl+X (ESP), Ctrl+A (Aimbot), Ctrl+H (UI)");
    console.log("[FGE] FEG © 2025");
    
    // 立即检查一次
    checkGameEngine();
    
    // 定期检查引擎状态
    engineCheckInterval = setInterval(checkGameEngine, 2000);
    
    // 清理
    window.addEventListener('beforeunload', () => {
        if (engineCheckInterval) clearInterval(engineCheckInterval);
        if (ESPInstance) ESPInstance.destroy();
    });
    
})();